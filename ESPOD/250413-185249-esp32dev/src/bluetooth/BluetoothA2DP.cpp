#include "BluetoothA2DP.h"
#include "BluetoothUtils.h"
#include "nvs_flash.h"

// Initialize static members
BluetoothA2DP* BluetoothA2DP::_instance = nullptr;
BluetoothA2DP btA2DP;

// Constructor
BluetoothA2DP::BluetoothA2DP() {
    _conn_state = A2DP_DISCONNECTED;
    _connected_device_name = "";
    _device_found_cb = nullptr;
    _device_count = 0;
    _device_index = 0;
    _instance = this;

    // Initialize connected BDA to zeros
    memset(_connected_bda, 0, sizeof(esp_bd_addr_t));
}

// Initialize Bluetooth stack and A2DP source profile
bool BluetoothA2DP::init(const char* device_name) {
    Serial.println("Initializing Bluetooth A2DP Source...");

    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // Release memory used by classic BT controller
    if (esp_bt_controller_mem_release(ESP_BT_MODE_BLE) != ESP_OK) {
        Serial.println("Failed to release memory of BLE mode");
        return false;
    }

    // Initialize controller with classic BT mode
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    if (esp_bt_controller_init(&bt_cfg) != ESP_OK) {
        Serial.println("Failed to initialize BT controller");
        return false;
    }

    // Enable controller in classic BT mode
    if (esp_bt_controller_enable(ESP_BT_MODE_CLASSIC_BT) != ESP_OK) {
        Serial.println("Failed to enable BT controller");
        return false;
    }

    // Initialize Bluedroid stack
    if (esp_bluedroid_init() != ESP_OK) {
        Serial.println("Failed to initialize Bluedroid stack");
        return false;
    }

    // Enable Bluedroid stack
    if (esp_bluedroid_enable() != ESP_OK) {
        Serial.println("Failed to enable Bluedroid stack");
        return false;
    }

    // Set device name
    if (esp_bt_dev_set_device_name(device_name) != ESP_OK) {
        Serial.println("Failed to set device name");
        return false;
    }

    // Register GAP callback
    if (esp_bt_gap_register_callback(gap_callback) != ESP_OK) {
        Serial.println("Failed to register GAP callback");
        return false;
    }

    // Set discoverable and connectable mode
    if (esp_bt_gap_set_scan_mode(ESP_BT_CONNECTABLE, ESP_BT_GENERAL_DISCOVERABLE) != ESP_OK) {
        Serial.println("Failed to set scan mode");
        return false;
    }

    // Register A2DP source callback
    if (esp_a2d_register_callback(a2dp_callback) != ESP_OK) {
        Serial.println("Failed to register A2DP callback");
        return false;
    }

    // Initialize A2DP source
    if (esp_a2d_source_init() != ESP_OK) {
        Serial.println("Failed to initialize A2DP source");
        return false;
    }

    // Register AVRC controller callback
    if (esp_avrc_ct_init() != ESP_OK) {
        Serial.println("Failed to initialize AVRC controller");
        return false;
    }

    if (esp_avrc_ct_register_callback(avrc_callback) != ESP_OK) {
        Serial.println("Failed to register AVRC callback");
        return false;
    }

    Serial.println("Bluetooth A2DP Source initialized successfully");
    return true;
}

// Start device discovery
bool BluetoothA2DP::startDiscovery(uint32_t duration_ms) {
    Serial.println("Starting Bluetooth device discovery...");

    // Reset device count and index
    _device_count = 0;
    _device_index = 0;

    // Set scan parameters
    esp_bt_gap_start_discovery(ESP_BT_INQ_MODE_GENERAL_INQUIRY, (duration_ms / 1280), 0);

    return true;
}

// Stop device discovery
bool BluetoothA2DP::stopDiscovery() {
    Serial.println("Stopping Bluetooth device discovery...");

    esp_bt_gap_cancel_discovery();

    return true;
}

// Connect to a device by MAC address
bool BluetoothA2DP::connect(const DeviceInfo& device) {
    if (_conn_state != A2DP_DISCONNECTED) {
        Serial.println("Cannot connect: already connected or connecting");
        return false;
    }

    Serial.print("Connecting to A2DP sink device: ");
    Serial.println(device.name);

    // Create a non-const copy of the address for the API
    uint8_t addr[ESP_BD_ADDR_LEN];
    memcpy(addr, device.addr, ESP_BD_ADDR_LEN);

    // Connect to A2DP sink
    if (esp_a2d_source_connect(addr) != ESP_OK) {
        Serial.println("Failed to connect to A2DP sink");
        return false;
    }

    _conn_state = A2DP_CONNECTING;
    _connected_device_name = device.name;
    memcpy(_connected_bda, device.addr, sizeof(esp_bd_addr_t));

    return true;
}

// Disconnect from current device
bool BluetoothA2DP::disconnect() {
    if (_conn_state != A2DP_CONNECTED) {
        Serial.println("Cannot disconnect: not connected");
        return false;
    }

    Serial.println("Disconnecting from A2DP sink device");

    if (esp_a2d_source_disconnect(_connected_bda) != ESP_OK) {
        Serial.println("Failed to disconnect from A2DP sink");
        return false;
    }

    _conn_state = A2DP_DISCONNECTING;

    return true;
}

// Get current connection state
a2dp_conn_state_t BluetoothA2DP::getConnectionState() {
    return _conn_state;
}

// Check if currently connected
bool BluetoothA2DP::isConnected() {
    return (_conn_state == A2DP_CONNECTED);
}

// Get the name of the connected device
String BluetoothA2DP::getConnectedDeviceName() {
    return _connected_device_name;
}

// Set callback for device discovery
void BluetoothA2DP::setDeviceFoundCallback(bt_device_found_cb callback) {
    _device_found_cb = callback;
}

// Get discovered devices
DeviceInfo* BluetoothA2DP::getDevices(int* count) {
    *count = _device_count;
    return _devices;
}

// AVRC control methods
bool BluetoothA2DP::play() {
    if (!isConnected()) {
        Serial.println("Cannot send play command: not connected");
        return false;
    }

    esp_err_t err = esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_PLAY, ESP_AVRC_PT_CMD_STATE_PRESSED);
    if (err == ESP_OK) {
        esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_PLAY, ESP_AVRC_PT_CMD_STATE_RELEASED);
        Serial.println("Play command sent");
        return true;
    } else {
        Serial.printf("Failed to send play command: %s\n", esp_err_to_name(err));
        return false;
    }
}

bool BluetoothA2DP::pause() {
    if (!isConnected()) {
        Serial.println("Cannot send pause command: not connected");
        return false;
    }

    esp_err_t err = esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_PAUSE, ESP_AVRC_PT_CMD_STATE_PRESSED);
    if (err == ESP_OK) {
        esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_PAUSE, ESP_AVRC_PT_CMD_STATE_RELEASED);
        Serial.println("Pause command sent");
        return true;
    } else {
        Serial.printf("Failed to send pause command: %s\n", esp_err_to_name(err));
        return false;
    }
}

bool BluetoothA2DP::stop() {
    if (!isConnected()) {
        Serial.println("Cannot send stop command: not connected");
        return false;
    }

    esp_err_t err = esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_STOP, ESP_AVRC_PT_CMD_STATE_PRESSED);
    if (err == ESP_OK) {
        esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_STOP, ESP_AVRC_PT_CMD_STATE_RELEASED);
        Serial.println("Stop command sent");
        return true;
    } else {
        Serial.printf("Failed to send stop command: %s\n", esp_err_to_name(err));
        return false;
    }
}

bool BluetoothA2DP::nextTrack() {
    if (!isConnected()) {
        Serial.println("Cannot send next track command: not connected");
        return false;
    }

    esp_err_t err = esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_FORWARD, ESP_AVRC_PT_CMD_STATE_PRESSED);
    if (err == ESP_OK) {
        esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_FORWARD, ESP_AVRC_PT_CMD_STATE_RELEASED);
        Serial.println("Next track command sent");
        return true;
    } else {
        Serial.printf("Failed to send next track command: %s\n", esp_err_to_name(err));
        return false;
    }
}

bool BluetoothA2DP::previousTrack() {
    if (!isConnected()) {
        Serial.println("Cannot send previous track command: not connected");
        return false;
    }

    esp_err_t err = esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_BACKWARD, ESP_AVRC_PT_CMD_STATE_PRESSED);
    if (err == ESP_OK) {
        esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_BACKWARD, ESP_AVRC_PT_CMD_STATE_RELEASED);
        Serial.println("Previous track command sent");
        return true;
    } else {
        Serial.printf("Failed to send previous track command: %s\n", esp_err_to_name(err));
        return false;
    }
}

bool BluetoothA2DP::volumeUp() {
    if (!isConnected()) {
        Serial.println("Cannot send volume up command: not connected");
        return false;
    }

    esp_err_t err = esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_VOL_UP, ESP_AVRC_PT_CMD_STATE_PRESSED);
    if (err == ESP_OK) {
        esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_VOL_UP, ESP_AVRC_PT_CMD_STATE_RELEASED);
        Serial.println("Volume up command sent");
        return true;
    } else {
        Serial.printf("Failed to send volume up command: %s\n", esp_err_to_name(err));
        return false;
    }
}

bool BluetoothA2DP::volumeDown() {
    if (!isConnected()) {
        Serial.println("Cannot send volume down command: not connected");
        return false;
    }

    esp_err_t err = esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_VOL_DOWN, ESP_AVRC_PT_CMD_STATE_PRESSED);
    if (err == ESP_OK) {
        esp_avrc_ct_send_passthrough_cmd(0, ESP_AVRC_PT_CMD_VOL_DOWN, ESP_AVRC_PT_CMD_STATE_RELEASED);
        Serial.println("Volume down command sent");
        return true;
    } else {
        Serial.printf("Failed to send volume down command: %s\n", esp_err_to_name(err));
        return false;
    }
}

// GAP callback
void BluetoothA2DP::gap_callback(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param) {
    if (!_instance) return;

    switch (event) {
        case ESP_BT_GAP_DISC_RES_EVT: {
            // Device found during discovery
            esp_bd_addr_t bda;
            memcpy(bda, param->disc_res.bda, sizeof(esp_bd_addr_t));

            // Get device name
            char device_name[ESP_BT_GAP_MAX_BDNAME_LEN + 1] = {0};
            uint8_t name_len = 0;
            esp_bt_gap_dev_prop_t *p = param->disc_res.prop;

            for (int i = 0; i < param->disc_res.num_prop; i++) {
                if (p[i].type == ESP_BT_GAP_DEV_PROP_BDNAME) {
                    name_len = (p[i].len < ESP_BT_GAP_MAX_BDNAME_LEN) ? p[i].len : ESP_BT_GAP_MAX_BDNAME_LEN;
                    memcpy(device_name, p[i].val, name_len);
                    device_name[name_len] = '\0';
                    break;
                }
            }

            // Get device type and class of device
            esp_bt_dev_type_t dev_type = ESP_BT_DEVICE_TYPE_BREDR;
            esp_bt_cod_t cod = {0};

            for (int i = 0; i < param->disc_res.num_prop; i++) {
                if (p[i].type == ESP_BT_GAP_DEV_PROP_COD) {
                    memcpy(&cod, p[i].val, sizeof(esp_bt_cod_t));
                }
            }

            // Check if this is a TWS device (headphone/audio device)
            if (isTWSDevice(cod)) {
                // Format MAC address as string
                char mac_str[18];
                sprintf(mac_str, "%02x:%02x:%02x:%02x:%02x:%02x",
                        bda[0], bda[1], bda[2], bda[3], bda[4], bda[5]);

                // Store device info
                _instance->_devices[_instance->_device_index].macAddress = String(mac_str);
                _instance->_devices[_instance->_device_index].name = String(device_name);
                memcpy(_instance->_devices[_instance->_device_index].addr, bda, sizeof(esp_bd_addr_t));

                // Increment device index and handle wrapping around
                _instance->_device_index = (_instance->_device_index + 1) % MAX_BT_DEVICES;

                // Increment device count only if it's less than MAX_BT_DEVICES
                if (_instance->_device_count < MAX_BT_DEVICES) {
                    _instance->_device_count++;
                }

                // Call device found callback if registered
                if (_instance->_device_found_cb) {
                    _instance->_device_found_cb(bda, dev_type, cod, device_name);
                }
            }

            break;
        }

        case ESP_BT_GAP_DISC_STATE_CHANGED_EVT:
            // Discovery state changed
            if (param->disc_st_chg.state == ESP_BT_GAP_DISCOVERY_STOPPED) {
                Serial.println("Device discovery stopped");
            } else if (param->disc_st_chg.state == ESP_BT_GAP_DISCOVERY_STARTED) {
                Serial.println("Device discovery started");
            }
            break;

        case ESP_BT_GAP_AUTH_CMPL_EVT:
            // Authentication completed
            if (param->auth_cmpl.stat == ESP_BT_STATUS_SUCCESS) {
                Serial.println("Authentication successful");
            } else {
                Serial.println("Authentication failed");
            }
            break;

        default:
            break;
    }
}

// A2DP callback
void BluetoothA2DP::a2dp_callback(esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param) {
    if (!_instance) return;

    switch (event) {
        case ESP_A2D_CONNECTION_STATE_EVT:
            // Connection state changed
            switch (param->conn_stat.state) {
                case ESP_A2D_CONNECTION_STATE_DISCONNECTED:
                    Serial.println("A2DP disconnected");
                    _instance->_conn_state = A2DP_DISCONNECTED;
                    memset(_instance->_connected_bda, 0, sizeof(esp_bd_addr_t));
                    break;

                case ESP_A2D_CONNECTION_STATE_CONNECTING:
                    Serial.println("A2DP connecting");
                    _instance->_conn_state = A2DP_CONNECTING;
                    break;

                case ESP_A2D_CONNECTION_STATE_CONNECTED:
                    Serial.println("A2DP connected");
                    _instance->_conn_state = A2DP_CONNECTED;
                    memcpy(_instance->_connected_bda, param->conn_stat.remote_bda, sizeof(esp_bd_addr_t));
                    break;

                case ESP_A2D_CONNECTION_STATE_DISCONNECTING:
                    Serial.println("A2DP disconnecting");
                    _instance->_conn_state = A2DP_DISCONNECTING;
                    break;

                default:
                    break;
            }
            break;

        case ESP_A2D_AUDIO_STATE_EVT:
            // Audio state changed
            switch (param->audio_stat.state) {
                case ESP_A2D_AUDIO_STATE_STARTED:
                    Serial.println("Audio streaming started");
                    break;

                case ESP_A2D_AUDIO_STATE_STOPPED:
                    Serial.println("Audio streaming stopped");
                    break;

                case ESP_A2D_AUDIO_STATE_REMOTE_SUSPEND:
                    Serial.println("Audio streaming suspended by remote");
                    break;

                default:
                    break;
            }
            break;

        default:
            break;
    }
}

// AVRC callback
void BluetoothA2DP::avrc_callback(esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param) {
    if (!_instance) return;

    switch (event) {
        case ESP_AVRC_CT_CONNECTION_STATE_EVT:
            // AVRC connection state changed
            if (param->conn_stat.connected) {
                Serial.println("AVRC connected");
            } else {
                Serial.println("AVRC disconnected");
            }
            break;

        default:
            break;
    }
}

// The isTWSDevice function is now defined in BluetoothUtils.h
