#include "Buttons.h"

// Declare globally so it's alive after setup()
gpio_num_t rowPins[2] = { GPIO_NUM_14, GPIO_NUM_27 };
gpio_num_t colPins[2] = { GPIO_NUM_13, GPIO_NUM_12 };
const char keymap[2][2] = { { '1', '2' }, { '3', '4' } };
Keypad2x2 keypad(rowPins, colPins, keymap);  // now global

void initButtons(KeypadCallback onKeyPress, LongPressCallback onLongPress){
  keypad.setCallback(onKeyPress);
  keypad.setLongPressCallback(onLongPress);
  keypad.begin();
}

void detachButtons(){
  keypad.destroy();
}
