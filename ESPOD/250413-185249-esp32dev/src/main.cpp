#include <Arduino.h>
#include "animations/Breather.h"
#include "bluetooth/BluetoothHelpers.h"
#include "bluetooth/BluetoothA2DP.h"
#include "ui/Buttons.h"
#include "core/DeviceState.h"
#include "bluetooth/SearchingTWS.h"
#include "utils/sleeper.h"

DeviceState currentState = SEARCHING_TWS;  // Start in SEARCHING_TWS state

// Function declarations
void onKeyPress(char key);
void onLongPress(char key);
void renderConnectedState();
void renderPlayingState();
void renderPausedState();

void setup() {
  Serial.begin(115200);
  Serial.println("\n\n=== ESPOD Bluetooth A2DP Source ===\n");

  modifyKeypadForNormalUse();
  initBreather();
  initButtons(onKeyPress, onLongPress);

  // Initialize Bluetooth
  initBluetooth();

  currentState = SEARCHING_TWS;
}

void loop() {
  switch (currentState) {
    case OFF:
      sleepWell();
      break;

    case SEARCHING_TWS:
      renderSearchingTWS();
      break;

    case CONNECTING:
      renderConnectingTWS();
      break;

    case CONNECTED:
      renderConnectedState();
      break;

    case PLAYING:
      renderPlayingState();
      break;

    case PAUSED:
      renderPausedState();
      break;

    default:
      Serial.print("Unknown state: ");
      Serial.println(currentState);
      currentState = SEARCHING_TWS; // Reset to a known state
  }

  delay(100);  // Keep the program running
}

// Render connected state
void renderConnectedState() {
  Serial.println("Connected to: " + btA2DP.getConnectedDeviceName());

  // Check if still connected
  if (!btA2DP.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(2000);
}

// Render playing state
void renderPlayingState() {
  Serial.println("Playing audio...");

  // Check if still connected
  if (!btA2DP.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

// Render paused state
void renderPausedState() {
  Serial.println("Audio paused");

  // Check if still connected
  if (!btA2DP.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

// Callback function to handle key press
void onKeyPress(char key) {
  Serial.print("Key Pressed: ");
  Serial.println(key);

  switch (currentState) {
    case SEARCHING_TWS:
      processBtnClickDuringSearchingTWS(key);
      break;

    case CONNECTED:
      if (key == '4') {
        // Start playing
        currentState = PLAYING;
      } else if (key == '1') {
        // Disconnect
        btA2DP.disconnect();
        currentState = SEARCHING_TWS;
      }
      break;

    case PLAYING:
      if (key == '4') {
        // Pause
        currentState = PAUSED;
      } else if (key == '1') {
        // Disconnect
        btA2DP.disconnect();
        currentState = SEARCHING_TWS;
      }
      break;

    case PAUSED:
      if (key == '4') {
        // Resume
        currentState = PLAYING;
      } else if (key == '1') {
        // Disconnect
        btA2DP.disconnect();
        currentState = SEARCHING_TWS;
      }
      break;

    default:
      Serial.println("NAFK"); // no action for key
  }
}

void onLongPress(char key) {
  Serial.printf("Long press detected on: %c\n", key);

  if (key == '2') {
    // Turn off device
    if (btA2DP.isConnected()) {
      btA2DP.disconnect();
    }
    currentState = OFF;
  }
}
