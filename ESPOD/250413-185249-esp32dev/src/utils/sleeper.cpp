#include "sleeper.h"
#include "../ui/Buttons.h"

void modifyKeypadForDeepSleep() {

  rtc_gpio_hold_dis(L1);
  rtc_gpio_hold_dis(L2);

  rtc_gpio_deinit(L1);
  rtc_gpio_set_direction_in_sleep(L1, RTC_GPIO_MODE_OUTPUT_ONLY);
  rtc_gpio_set_level(L1, 0);
  rtc_gpio_hold_en(L1);

  rtc_gpio_deinit(L2);
  rtc_gpio_set_direction_in_sleep(L2, RTC_GPIO_MODE_OUTPUT_ONLY);
  rtc_gpio_set_level(L2, 0);
  rtc_gpio_hold_en(L2);

  // --- Configure ROW pins (R1) as RTC GPIO INPUT with pull-up
  rtc_gpio_deinit(R1);
  rtc_gpio_set_direction_in_sleep(R1, RTC_GPIO_MODE_INPUT_ONLY);
  rtc_gpio_pullup_en(R1);

  // You don't need to wake on R2 (GPIO 27) — leave it floating or input if desired
  rtc_gpio_deinit(R2);
  rtc_gpio_set_direction_in_sleep(R2, RTC_GPIO_MODE_INPUT_ONLY);
  rtc_gpio_pullup_en(R2);  // optional
}

void modifyKeypadForNormalUse() {
  // Disable holds
  rtc_gpio_hold_dis(L1);
  rtc_gpio_hold_dis(L2);

  // Deinit RTC GPIO function, so they go back to normal GPIO control
  rtc_gpio_deinit(L1);
  rtc_gpio_deinit(L2);
  rtc_gpio_deinit(R1);
  rtc_gpio_deinit(R2);
}

void sleepWell() {
  Serial.println("I am sleeping now..");
  detachButtons();
  delay(500);
  modifyKeypadForDeepSleep();
  esp_sleep_enable_ext0_wakeup(R1, 0);  // wake if pin 14 goes LOW
  esp_deep_sleep_start();
}
